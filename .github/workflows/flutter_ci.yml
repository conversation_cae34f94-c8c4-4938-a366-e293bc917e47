name: Flutter CI

on:
  push:
    branches: [ develop, main ]
    paths:
      - 'flutter/**'
      - '.github/workflows/flutter_ci.yml'
  pull_request:
    branches: [ develop, main ]
    paths:
      - 'flutter/**'
      - '.github/workflows/flutter_ci.yml'

jobs:
  analyze:
    name: Analyze and Format Check
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: flutter

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .

      - name: Analyze project source
        run: flutter analyze --fatal-infos

      - name: Check import sorting
        run: |
          flutter pub global activate import_sorter
          dart run import_sorter:main --exit-if-changed

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: flutter

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Run tests
        run: flutter test --coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: flutter/coverage/lcov.info
          flags: flutter
          name: flutter-coverage

  build:
    name: Build APK
    runs-on: ubuntu-latest
    needs: [analyze, test]
    defaults:
      run:
        working-directory: flutter

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Build APK
        run: flutter build apk --debug

      - name: Upload APK artifact
        uses: actions/upload-artifact@v3
        with:
          name: debug-apk
          path: flutter/build/app/outputs/flutter-apk/app-debug.apk

  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: [analyze, test]
    defaults:
      run:
        working-directory: flutter

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
          channel: 'stable'
          cache: true

      - name: Get dependencies
        run: flutter pub get

      - name: Build iOS (no codesign)
        run: flutter build ios --no-codesign --debug
