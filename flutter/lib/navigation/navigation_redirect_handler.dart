import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';

/// Обработчик редиректов для навигации
/// Выделен в отдельный класс для упрощения тестирования
class NavigationRedirectHandler {
  /// Обрабатывает редиректы на основе состояния авторизации
  static String? handleRedirect(
    GoRouterState state,
    AuthState authState,
  ) {
    final currentLocation = state.matchedLocation;

    if (kDebugMode) {
      debugPrint('🧭 [NavigationRedirect] Checking redirect for: $currentLocation');
      debugPrint('   Auth state: loading=${authState.isLoading}, authenticated=${authState.isAuthenticated}');
    }

    // Если идет загрузка, остаемся на splash экране
    if (authState.isLoading) {
      if (currentLocation != '/splash') {
        if (kDebugMode) {
          debugPrint('   🔄 Loading state, redirecting to splash');
        }
        return '/splash';
      }
      return null;
    }

    // Если пользователь не авторизован
    if (!authState.isAuthenticated) {
      // Разрешенные маршруты для неавторизованных пользователей
      final allowedRoutes = ['/auth', '/splash'];
      
      if (!allowedRoutes.contains(currentLocation)) {
        if (kDebugMode) {
          debugPrint('   🔒 Not authenticated, redirecting to auth');
        }
        return '/auth';
      }
      
      if (kDebugMode) {
        debugPrint('   ✅ Not authenticated but on allowed route, no redirect');
      }
      return null;
    }

    // Если пользователь авторизован
    if (authState.isAuthenticated) {
      // Если пользователь на экране авторизации, перенаправляем на главную
      if (currentLocation == '/auth') {
        if (kDebugMode) {
          debugPrint('   🚀 Authenticated on auth screen, redirecting to /news');
        }
        return '/news';
      }
      
      // Если пользователь на splash экране, перенаправляем на главную
      if (currentLocation == '/splash') {
        if (kDebugMode) {
          debugPrint('   🚀 Authenticated on splash screen, redirecting to /news');
        }
        return '/news';
      }
      
      if (kDebugMode) {
        debugPrint('   ✅ Authenticated and on protected route, no redirect');
      }
      return null;
    }

    return null;
  }

  /// Проверяет, требует ли маршрут авторизации
  static bool requiresAuth(String location) {
    final publicRoutes = ['/auth', '/splash'];
    return !publicRoutes.contains(location);
  }

  /// Проверяет, является ли маршрут публичным
  static bool isPublicRoute(String location) {
    return !requiresAuth(location);
  }

  /// Получает маршрут по умолчанию для авторизованных пользователей
  static String getDefaultAuthenticatedRoute() {
    return '/news';
  }

  /// Получает маршрут по умолчанию для неавторизованных пользователей
  static String getDefaultUnauthenticatedRoute() {
    return '/auth';
  }
}
