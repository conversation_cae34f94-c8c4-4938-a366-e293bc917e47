import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/core/services/permission_service.dart';
import 'package:luxury_app/features/drawer/app_drawer.dart';
import 'package:luxury_app/features/drawer/resizable_drawer.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/widgets/content_wrapper.dart';
import 'package:luxury_app/shared/widgets/global_audio_player.dart';

/// Оболочка приложения с Scaffold и боковой панелью
class AppShell extends ConsumerStatefulWidget {
  /// Основное содержимое приложения
  final Widget body;

  const AppShell({super.key, required this.body});

  @override
  ConsumerState<AppShell> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final bool showSideMenu = width >= AppSizes.mobileBreakpoint;

    // Всегда используем widget.body из роутера
    final Widget content = RepaintBoundary(
      child: ContentWrapper(
        contentKey: PageStorageKey('content-${widget.body.hashCode}'),
        child: widget.body,
      ),
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        final Widget mainLayout =
            !showSideMenu
                ? _buildMobileLayout(content)
                : _buildDesktopLayoutOptimized(constraints, content);

        // Оборачиваем в Stack для отображения глобального аудиоплеера
        return Stack(children: [mainLayout, const GlobalAudioPlayer()]);
      },
    );
  }

  /// Построение AppBar
  AppBar _buildAppBar(
    AppState state,
    String title, {
    bool showMenuButton = true,
  }) {
    return AppBar(
      title: Text(title),
      leading:
          showMenuButton
              ? IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () {
                  _scaffoldKey.currentState?.openDrawer();
                },
              )
              : null,
      actions: _buildAppBarActions(state),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
      scrolledUnderElevation: 0,
      surfaceTintColor: Colors.transparent,
    );
  }

  /// Построение действий для AppBar
  List<Widget>? _buildAppBarActions(AppState state) {
    if (state.activeScreen == DrawerMode.news) {
      return [const _NewsAppBarAction()];
    }
    return null;
  }

  /// Построение мобильного макета
  Widget _buildMobileLayout(Widget content) {
    const double minWidth = 360;
    const double maxWidth = 900;

    return LayoutBuilder(
      builder: (context, constraints) {
        final double clampedWidth = constraints.maxWidth.clamp(
          minWidth,
          maxWidth,
        );

        return Consumer(
          builder: (context, ref, child) {
            final state = ref.watch(appProvider);

            // Определяем заголовок по умолчанию в зависимости от активного экрана
            String getDefaultTitle() {
              switch (state.activeScreen) {
                case DrawerMode.news:
                  return 'Новости';
                case DrawerMode.wiki:
                  return 'База знаний';
                case DrawerMode.chat:
                  return 'ИИ-ассистент';
                case DrawerMode.users:
                  return 'Пользователи';
              }
            }

            return Scaffold(
              key: _scaffoldKey,
              appBar: _buildAppBar(
                state,
                state.currentScreenTitle ?? getDefaultTitle(),
                showMenuButton: true,
              ),
              drawer: AppDrawer(
                scaffoldKey: _scaffoldKey,
                roundedCorners: true,
              ),
              body: Align(
                alignment: Alignment.topCenter,
                child: SizedBox(width: clampedWidth, child: content),
              ),
            );
          },
        );
      },
    );
  }

  /// Построение десктопного макета (оптимизированная версия)
  Widget _buildDesktopLayoutOptimized(
    BoxConstraints constraints,
    Widget content,
  ) {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(appProvider);
        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: Row(
            children: [
              // Левая панель
              ResizableDrawer(scaffoldKey: _scaffoldKey),
              // Основной контент
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final drawerWidth = ref.watch(
                      appProvider.select((state) => state.drawerWidth),
                    );
                    return _buildDesktopContent(
                      constraints,
                      content,
                      drawerWidth,
                      state,
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Построение контента для desktop с адаптивной шириной
  Widget _buildDesktopContent(
    BoxConstraints constraints,
    Widget content,
    double drawerWidth,
    AppState state,
  ) {
    const double minContentWidth = 600;
    const double maxContentWidth = 1200;

    final double availableWidth = constraints.maxWidth - drawerWidth;
    final double contentWidth = availableWidth.clamp(
      minContentWidth,
      maxContentWidth,
    );

    return Center(
      child: SizedBox(
        width: contentWidth,
        child: Column(
          children: [
            _buildAppBar(
              state,
              state.currentScreenTitle ?? '',
              showMenuButton: false,
            ),
            Expanded(child: content),
          ],
        ),
      ),
    );
  }
}

/// Кнопка для AppBar на экране новостей
class _NewsAppBarAction extends ConsumerWidget {
  const _NewsAppBarAction();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasPermission =
        ref.watch(permissionServiceProvider).hasPermission(Permissions.canCreateNews);
    final newsState = ref.watch(newsProvider);

    // Показываем кнопку только если есть права и новости не загружаются
    if (hasPermission && !newsState.isLoading) {
      return Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: IconButton(
          onPressed: () {
            ref.read(newsEditingProvider.notifier).startCreatingNews();
          },
          icon: const Icon(LucideIcons.plus),
          tooltip: 'Добавить новость',
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
