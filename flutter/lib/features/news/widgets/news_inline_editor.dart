import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/shared/constants/sizes.dart';

/// Виджет для inline редактирования новостей
class NewsInlineEditor extends ConsumerStatefulWidget {
  /// ID новости (null для создания новой)
  final int? newsId;

  /// Начальное содержимое
  final String initialContent;

  /// Callback при сохранении
  final VoidCallback? onSaved;

  /// Callback при отмене
  final VoidCallback? onCancelled;

  const NewsInlineEditor({
    super.key,
    this.newsId,
    required this.initialContent,
    this.onSaved,
    this.onCancelled,
  });

  @override
  ConsumerState<NewsInlineEditor> createState() => _NewsInlineEditorState();
}

class _NewsInlineEditorState extends ConsumerState<NewsInlineEditor> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialContent);
    _focusNode = FocusNode();

    // Автофокус для новых новостей
    if (widget.newsId == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }

    _controller.addListener(_onContentChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onContentChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    final hasChanges = _controller.text != widget.initialContent;
    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }

    // Обновляем содержимое в провайдере
    ref
        .read(newsEditingProvider.notifier)
        .updateEditingContent(_controller.text);
  }

  Future<void> _save() async {
    final editingNotifier = ref.read(newsEditingProvider.notifier);

    try {
      await editingNotifier.saveChanges();

      // Список новостей обновится автоматически через подписку
      // await newsNotifier.refreshNews();

      widget.onSaved?.call();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.newsId == null ? 'Новость создана' : 'Новость обновлена',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка сохранения: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _cancel() {
    ref.read(newsEditingProvider.notifier).cancelEditing();
    widget.onCancelled?.call();
  }

  @override
  Widget build(BuildContext context) {
    final editingState = ref.watch(newsEditingProvider);
    final isSaving = editingState.isSaving;

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.surface,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Заголовок
          Row(
            children: [
              Icon(
                widget.newsId == null ? LucideIcons.plus : LucideIcons.edit,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                widget.newsId == null
                    ? 'Создание новости'
                    : 'Редактирование новости',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSizes.paddingM),

          // Поле ввода
          TextField(
            controller: _controller,
            focusNode: _focusNode,
            maxLines: null,
            minLines: 3,
            enabled: !isSaving,
            decoration: InputDecoration(
              hintText: 'Введите содержимое новости в формате Markdown...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(AppSizes.paddingM),
            ),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontFamily: 'monospace'),
          ),

          const SizedBox(height: AppSizes.paddingM),

          // Кнопки действий
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: isSaving ? null : _cancel,
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                child: const Text('Отмена'),
              ),

              const SizedBox(width: AppSizes.paddingS),

              FilledButton(
                onPressed:
                    isSaving || _controller.text.trim().isEmpty ? null : _save,
                style: FilledButton.styleFrom(minimumSize: const Size(100, 40)),
                child:
                    isSaving
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : Text(
                          widget.newsId == null ? 'Опубликовать' : 'Сохранить',
                        ),
              ),
            ],
          ),

          // Подсказка по Markdown
          if (widget.newsId == null) ...[
            const SizedBox(height: AppSizes.paddingS),
            Text(
              'Поддерживается Markdown: **жирный**, *курсив*, [ссылка](url), # заголовок',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
