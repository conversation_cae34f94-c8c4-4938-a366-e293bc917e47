import 'dart:async';


import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/models/data_state.dart';
import 'package:luxury_app/core/services/app_preload_service.dart';

import 'news_item.dart';
import 'news_repository.dart';

/// Состояние для inline редактирования новостей
class NewsEditingState {
  /// ID новости, которая редактируется (null если создается новая)
  final int? editingNewsId;

  /// Содержимое в процессе редактирования
  final String editingContent;

  /// Флаг создания новой новости
  final bool isCreatingNew;

  /// Флаг сохранения
  final bool isSaving;

  const NewsEditingState({
    this.editingNewsId,
    this.editingContent = '',
    this.isCreatingNew = false,
    this.isSaving = false,
  });

  NewsEditingState copyWith({
    int? editingNewsId,
    String? editingContent,
    bool? isCreatingNew,
    bool? isSaving,
  }) {
    return NewsEditingState(
      editingNewsId: editingNewsId ?? this.editingNewsId,
      editingContent: editingContent ?? this.editingContent,
      isCreatingNew: isCreatingNew ?? this.isCreatingNew,
      isSaving: isSaving ?? this.isSaving,
    );
  }

  /// Сброс состояния редактирования
  NewsEditingState reset() {
    return const NewsEditingState();
  }

  /// Проверка, редактируется ли конкретная новость
  bool isEditingNews(int newsId) {
    return editingNewsId == newsId && !isCreatingNew;
  }

  /// Проверка, есть ли активное редактирование
  bool get hasActiveEditing {
    return isCreatingNew || editingNewsId != null;
  }
}

/// Нотификатор для управления состоянием новостей
class NewsNotifier extends StateNotifier<DataState<List<NewsItem>>> {
  final NewsRepository _newsRepository;
  final AppPreloadService _preloadService;
  StreamSubscription<List<NewsItem>>? _newsSubscription;

  NewsNotifier(this._newsRepository, this._preloadService)
    : super(const DataState<List<NewsItem>>()) {
    _initialize();
  }

  @override
  void dispose() {
    _newsSubscription?.cancel();
    super.dispose();
  }

  /// Инициализация новостей
  Future<void> _initialize() async {
    // Используем предзагруженные данные как начальное состояние
    final preloadedNews = await _preloadService.getPreloadedNews();
    if (preloadedNews != null && preloadedNews.isNotEmpty) {
      state = DataState(data: preloadedNews, source: DataSource.cache);
    } else {
      state = const DataState(isLoading: true);
    }

    // Подписываемся на обновления
    _newsSubscription?.cancel();
    _newsSubscription = _newsRepository.subscribeToNews().listen(
      (newsList) {
        state = DataState(data: newsList, source: DataSource.network);
      },
      onError: (error) {
        state = DataState(error: 'Ошибка загрузки новостей: $error');
      },
    );
  }

  /// Принудительное обновление (для RefreshIndicator)
  Future<void> refreshNews() async {
    try {
      state = state.copyWithLoading();
      final newsList = await _newsRepository.getNews(forceRefresh: true);
      state = state.copyWithData(newsList, source: DataSource.network);
    } catch (e) {
      state = state.copyWithError('Ошибка обновления новостей: $e');
    }
  }

  /// Создание новой новости
  Future<NewsItem> createNews(String content) async {
    try {
      return await _newsRepository.createNews(content);
    } catch (e) {
      state = state.copyWithError('Ошибка создания новости: $e');
      rethrow;
    }
  }

  /// Обновление существующей новости
  Future<NewsItem> updateNews(int id, String content) async {
    try {
      return await _newsRepository.updateNews(id, content);
    } catch (e) {
      state = state.copyWithError('Ошибка обновления новости: $e');
      rethrow;
    }
  }

  /// Удаление новости
  Future<void> deleteNews(int id) async {
    try {
      await _newsRepository.deleteNews(id);
    } catch (e) {
      state = state.copyWithError('Ошибка удаления новости: $e');
      rethrow;
    }
  }
}

/// Нотификатор для управления состоянием inline редактирования новостей
class NewsEditingNotifier extends StateNotifier<NewsEditingState> {
  final NewsRepository _newsRepository;

  NewsEditingNotifier(this._newsRepository) : super(const NewsEditingState());

  /// Начать создание новой новости
  void startCreatingNews() {
    state = state.copyWith(
      isCreatingNew: true,
      editingContent: '',
      editingNewsId: null,
    );
  }

  /// Начать редактирование существующей новости
  void startEditingNews(int newsId, String currentContent) {
    state = state.copyWith(
      editingNewsId: newsId,
      editingContent: currentContent,
      isCreatingNew: false,
    );
  }

  /// Обновить содержимое в процессе редактирования
  void updateEditingContent(String content) {
    state = state.copyWith(editingContent: content);
  }

  /// Сохранить изменения
  Future<void> saveChanges() async {
    if (!state.hasActiveEditing) return;

    state = state.copyWith(isSaving: true);

    try {
      if (state.isCreatingNew) {
        await _newsRepository.createNews(state.editingContent);
      } else if (state.editingNewsId != null) {
        await _newsRepository.updateNews(
          state.editingNewsId!,
          state.editingContent,
        );
      }

      // Сбрасываем состояние после успешного сохранения
      state = state.reset();
    } catch (e) {
      // В случае ошибки убираем флаг сохранения, но оставляем редактирование активным
      state = state.copyWith(isSaving: false);
      rethrow;
    }
  }

  /// Отменить редактирование
  void cancelEditing() {
    state = state.reset();
  }
}

/// Провайдер для управления состоянием новостей
final newsProvider =
    StateNotifierProvider<NewsNotifier, DataState<List<NewsItem>>>((ref) {
      return NewsNotifier(
        ref.read(newsRepositoryProvider),
        ref.read(appPreloadServiceProvider),
      );
    });

/// Провайдер для управления состоянием inline редактирования новостей
final newsEditingProvider =
    StateNotifierProvider<NewsEditingNotifier, NewsEditingState>((ref) {
      return NewsEditingNotifier(ref.read(newsRepositoryProvider));
    });
