import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/errors/error_notifier.dart';
import 'package:luxury_app/core/services/app_preload_service.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/auth_repository.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/supabase_auth_repository.dart';

/// Состояние аутентификации
class AuthState {
  final User? user;
  final bool isLoading;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.isAuthenticated = false,
  });

  AuthState copyWith({User? user, bool? isLoading, bool? isAuthenticated}) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Notifier для управления состоянием аутентификации
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final ErrorNotifier _errorNotifier;
  final AppPreloadService _preloadService;
  final ProviderContainer _container;

  StreamSubscription<dynamic>? _authStateSubscription;

  AuthNotifier(
    this._authRepository,
    this._errorNotifier,
    this._preloadService,
    this._container,
  ) : super(const AuthState(isLoading: true)) {
    _initializeAuth();
  }

  bool _isCheckingAuthStatus = false;

  Future<void> _initializeAuth() async {
    try {
      // Сначала пытаемся загрузить сохраненный токен
      await _authRepository.loadSavedToken();

      // Проверяем статус авторизации (работает и в офлайн режиме)
      await _checkAuthStatus();

      // Подписка на изменения состояния авторизации (только для онлайн режима)
      if (_authRepository is SupabaseAuthRepository) {
        final repo = _authRepository;

        // Подписка на изменения состояния авторизации
        _authStateSubscription = repo.authStateChanges.listen((_) {
          _checkAuthStatus();
        });

        // Пытаемся обновить сессию только если есть подключение
        // Это не критично для работы приложения
        _tryRefreshSessionInBackground();
      }
    } catch (e) {
      // При ошибке инициализации переходим в неавторизованное состояние
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );
    }
  }

  /// Пытается обновить сессию в фоновом режиме (не блокирует инициализацию)
  Future<void> _tryRefreshSessionInBackground() async {
    final repo = _authRepository;
    if (repo is SupabaseAuthRepository) {
      try {
        await repo.supabaseService.client.auth.refreshSession();
      } catch (e) {
        // Игнорируем ошибки обновления сессии - это не критично
        // Приложение должно работать с локально сохраненными данными
      }
    }
  }

  /// Проверяет статус аутентификации
  Future<void> _checkAuthStatus() async {
    // Предотвращаем одновременные вызовы
    if (_isCheckingAuthStatus) return;
    _isCheckingAuthStatus = true;

    try {
      state = state.copyWith(isLoading: true);

      final isAuth = await _authRepository.isAuthenticated();
      if (!isAuth) {
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
          user: null,
        );
        return;
      }

      try {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          // Запускаем предзагрузку данных перед завершением состояния загрузки
          await _startPreloadingIfNeeded();

          state = state.copyWith(
            isLoading: false,
            isAuthenticated: true,
            user: user,
          );
          return;
        }

        await _authRepository.logout();
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
          user: null,
        );
      } catch (profileError) {
        final errorString = profileError.toString();
        if (errorString.contains('401') ||
            errorString.contains('Unauthorized')) {
          await _authRepository.logout();
          state = state.copyWith(
            isLoading: false,
            isAuthenticated: false,
            user: null,
          );
        } else {
          _errorNotifier.showError('Ошибка загрузки профиля: $errorString');
          state = state.copyWith(
            isLoading: false,
            isAuthenticated: false,
            user: null,
          );
        }
      }
    } catch (e) {
      _errorNotifier.showError('Ошибка проверки авторизации: ${e.toString()}');
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );
    } finally {
      _isCheckingAuthStatus = false;
    }
  }

  /// Регистрация пользователя
  Future<void> register(String email, String password, {String? phone}) async {
    state = state.copyWith(isLoading: true);

    try {
      final user = await _authRepository.register(
        email,
        password,
        phone: phone,
      );
      await _authRepository.login(email, password);

      state = state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        user: user,
      );
    } catch (e) {
      _errorNotifier.showError(e.toString());
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );
    }
  }

  /// Вход пользователя
  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true);

    try {
      await _authRepository.login(email, password, rememberMe: true);

      try {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          // Запускаем предзагрузку данных перед завершением состояния загрузки
          await _startPreloadingIfNeeded();

          state = state.copyWith(
            isLoading: false,
            isAuthenticated: true,
            user: user,
          );

          if (kDebugMode) {
            debugPrint('✅ [AuthProvider] Login successful: ${user.email}');
            debugPrint('   State updated: isAuthenticated=true');
          }
          return;
        }

        _errorNotifier.showError('Ошибка получения данных пользователя');
        await _authRepository.logout();
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
          user: null,
        );
      } catch (profileError) {
        _errorNotifier.showError(
          'Ошибка загрузки профиля: ${profileError.toString()}',
        );
        await _authRepository.logout();
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
          user: null,
        );
      }
    } catch (e) {
      _errorNotifier.showError(e.toString());
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );
    }
  }

  /// Сброс пароля по email
  Future<void> resetPassword(String email) async {
    try {
      await _authRepository.resetPassword(email: email);
    } catch (e) {
      _errorNotifier.showError(e.toString());
      rethrow;
    }
  }

  /// Выход пользователя
  Future<void> logout() async {
    if (kDebugMode) {
      debugPrint('🚪 [AuthProvider] Logout initiated');
    }

    state = state.copyWith(isLoading: true);

    try {
      await _authRepository.logout();
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );

      if (kDebugMode) {
        debugPrint('✅ [AuthProvider] Logout successful');
        debugPrint('   State updated: isAuthenticated=false');
      }
    } catch (e) {
      _errorNotifier.showError(e.toString());
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );

      if (kDebugMode) {
        debugPrint('❌ [AuthProvider] Logout error: $e');
        debugPrint('   State updated: isAuthenticated=false');
      }
    }
  }

  /// Принудительная проверка статуса аутентификации
  Future<void> checkAuthStatus() async {
    await _checkAuthStatus();
  }

  /// Запускает предзагрузку данных для авторизованного пользователя
  Future<void> _startPreloadingIfNeeded() async {
    try {
      if (kDebugMode) {
        debugPrint('🚀 [AuthProvider] Запуск предзагрузки данных...');
      }

      // Инициализируем сервис предзагрузки с необходимыми зависимостями
      // Это нужно делать здесь, так как провайдеры могут быть недоступны в конструкторе
      _preloadService.initialize(
        newsRepository: _container.read(newsRepositoryProvider),
        permissionService: _container.read(permissionServiceProvider),
      );

      // Запускаем предзагрузку
      await _preloadService.startPreloading();

      if (kDebugMode) {
        debugPrint('✅ [AuthProvider] Предзагрузка завершена');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ [AuthProvider] Ошибка предзагрузки: $e');
      }
      // Не прерываем процесс авторизации из-за ошибки предзагрузки
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}

// Провайдеры импортированы из core/providers/app_providers.dart
// - supabaseServiceProvider
// - cacheServiceProvider
// - authCacheServiceProvider
// - errorNotifierProvider
// - authRepositoryProvider

/// Основной провайдер для состояния аутентификации
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(
    ref.read(authRepositoryProvider),
    ref.read(errorNotifierProvider),
    ref.read(appPreloadServiceProvider),
    ref.container,
  );
});
