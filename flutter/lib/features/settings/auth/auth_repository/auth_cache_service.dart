import 'dart:async';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';

/// Сервис для кэширования данных аутентификации
class AuthCacheService with LoggerMixin {
  // Константы для ключей кэширования
  static const String _tokenKey = 'auth_token';
  static const String _emailKey = 'user_email';

  /// Экземпляр Hive box
  Box? _box;

  /// Completer для синхронизации инициализации
  Completer<void>? _initCompleter;

  /// Приватный конструктор для синглтона
  AuthCacheService._();

  /// Глобальный экземпляр сервиса
  static final AuthCacheService instance = AuthCacheService._();

  /// Публичный геттер для box (только для чтения)
  Box? get box => _box;

  /// Инициализирует сервис кэширования
  Future<bool> init() async {
    // Если уже инициализирован, возвращаем
    if (_box != null) return true;

    // Если инициализация уже идет, ждем ее завершения
    if (_initCompleter != null) {
      try {
        await _initCompleter!.future;
        return _box != null;
      } catch (e) {
        return false;
      }
    }

    // Начинаем инициализацию
    _initCompleter = Completer<void>();

    try {
      await Hive.initFlutter();
      _box = await Hive.openBox('auth_cache');
      _initCompleter!.complete();
      return true;
    } catch (e) {
      _initCompleter!.completeError(e);
      _initCompleter = null;
      return false;
    }
  }

  /// Сохраняет токен аутентификации
  Future<bool> saveAuthToken(String token) async {
    return await _safePut(_tokenKey, token);
  }

  /// Получает токен аутентификации
  String? getAuthToken() {
    return _safeGet<String>(_tokenKey);
  }

  /// Сохраняет email пользователя
  Future<bool> saveUserEmail(String email) async {
    return await _safePut(_emailKey, email);
  }

  /// Получает email пользователя
  String? getUserEmail() {
    return _safeGet<String>(_emailKey);
  }

  /// Очищает данные аутентификации (токен и email)
  Future<void> clearAuthData() async {
    await init(); // Обеспечиваем инициализацию
    await _box?.delete(_tokenKey);
    await _box?.delete(_emailKey);
  }

  /// Безопасно получает данные с автоматической инициализацией
  T? _safeGet<T>(String key) {
    try {
      if (_box == null) {
        // Не запускаем асинхронную инициализацию в синхронном методе
        // Возвращаем null, чтобы избежать race condition
        return null;
      }
      final value = _box!.get(key);
      return value is T ? value : null;
    } catch (e) {
      logError('Ошибка при безопасном получении данных: $key', e);
      return null;
    }
  }

  /// Безопасно сохраняет данные с проверкой инициализации
  Future<bool> _safePut(String key, dynamic value) async {
    try {
      final initialized = await init();
      if (initialized && _box != null) {
        await _box!.put(key, value);
        return true;
      }
      return false;
    } catch (e) {
      logError('Ошибка при сохранении в кэш: $key', e);
      return false;
    }
  }
}
