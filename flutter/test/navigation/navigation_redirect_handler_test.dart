import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/navigation/navigation_redirect_handler.dart';

// Mock class for testing
class MockGoRouterState {
  final String location;
  final String matchedLocation;

  MockGoRouterState(this.location) : matchedLocation = location;
}

void main() {
  group('NavigationRedirectHandler', () {
    group('handleRedirect', () {
      test('redirects to splash when loading', () {
        final authState = AsyncLoading<User?>();
        final state = MockGoRouterState('/news');

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, '/splash');
      });

      test('returns null when already on splash and loading', () {
        final authState = const AuthState(isLoading: true);
        final state = GoRouterState(
          location: '/splash',
          matchedLocation: '/splash',
          name: 'splash',
          path: '/splash',
          fullPath: '/splash',
          params: {},
          queryParams: {},
          extra: null,
          error: null,
          pageKey: const ValueKey('splash'),
        );

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, null);
      });

      test('redirects to auth when not authenticated', () {
        final authState = const AuthState(
          isAuthenticated: false,
          isLoading: false,
        );
        final state = GoRouterState(
          location: '/news',
          matchedLocation: '/news',
          name: 'news',
          path: '/news',
          fullPath: '/news',
          params: {},
          queryParams: {},
          extra: null,
          error: null,
          pageKey: const ValueKey('news'),
        );

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, '/auth');
      });

      test('returns null when not authenticated and on auth screen', () {
        final authState = const AuthState(
          isAuthenticated: false,
          isLoading: false,
        );
        final state = GoRouterState(
          location: '/auth',
          matchedLocation: '/auth',
          name: 'auth',
          path: '/auth',
          fullPath: '/auth',
          params: {},
          queryParams: {},
          extra: null,
          error: null,
          pageKey: const ValueKey('auth'),
        );

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, null);
      });

      test('redirects to news when authenticated and on auth screen', () {
        final user = User(
          id: '1',
          email: '<EMAIL>',
          role: 'user',
          createdAt: DateTime.now(),
        );
        final authState = AuthState(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );
        final state = GoRouterState(
          location: '/auth',
          matchedLocation: '/auth',
          name: 'auth',
          path: '/auth',
          fullPath: '/auth',
          params: {},
          queryParams: {},
          extra: null,
          error: null,
          pageKey: const ValueKey('auth'),
        );

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, '/news');
      });

      test('redirects to news when authenticated and on splash screen', () {
        final user = User(
          id: '1',
          email: '<EMAIL>',
          role: 'user',
          createdAt: DateTime.now(),
        );
        final authState = AuthState(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );
        final state = GoRouterState(
          location: '/splash',
          matchedLocation: '/splash',
          name: 'splash',
          path: '/splash',
          fullPath: '/splash',
          params: {},
          queryParams: {},
          extra: null,
          error: null,
          pageKey: const ValueKey('splash'),
        );

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, '/news');
      });

      test('returns null when authenticated and on protected route', () {
        final user = User(
          id: '1',
          email: '<EMAIL>',
          role: 'user',
          createdAt: DateTime.now(),
        );
        final authState = AuthState(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );
        final state = GoRouterState(
          location: '/news',
          matchedLocation: '/news',
          name: 'news',
          path: '/news',
          fullPath: '/news',
          params: {},
          queryParams: {},
          extra: null,
          error: null,
          pageKey: const ValueKey('news'),
        );

        final result = NavigationRedirectHandler.handleRedirect(
          state,
          authState,
        );

        expect(result, null);
      });
    });

    group('helper methods', () {
      test('requiresAuth returns true for protected routes', () {
        expect(NavigationRedirectHandler.requiresAuth('/news'), true);
        expect(NavigationRedirectHandler.requiresAuth('/wiki/123'), true);
        expect(NavigationRedirectHandler.requiresAuth('/ai/456'), true);
      });

      test('requiresAuth returns false for public routes', () {
        expect(NavigationRedirectHandler.requiresAuth('/auth'), false);
        expect(NavigationRedirectHandler.requiresAuth('/splash'), false);
      });

      test('isPublicRoute returns correct values', () {
        expect(NavigationRedirectHandler.isPublicRoute('/auth'), true);
        expect(NavigationRedirectHandler.isPublicRoute('/splash'), true);
        expect(NavigationRedirectHandler.isPublicRoute('/news'), false);
      });

      test('getDefaultAuthenticatedRoute returns /news', () {
        expect(
          NavigationRedirectHandler.getDefaultAuthenticatedRoute(),
          '/news',
        );
      });

      test('getDefaultUnauthenticatedRoute returns /auth', () {
        expect(
          NavigationRedirectHandler.getDefaultUnauthenticatedRoute(),
          '/auth',
        );
      });
    });
  });
}
