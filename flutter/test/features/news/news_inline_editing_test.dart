import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/features/news/widgets/news_inline_editor.dart';
import 'package:mocktail/mocktail.dart';

// Mocks
class MockNewsEditingNotifier extends StateNotifier<NewsEditingState>
    with <PERSON><PERSON>
    implements NewsEditingNotifier {
  MockNewsEditingNotifier(super.state);
}

void main() {
  group('NewsInlineEditor', () {
    late MockNewsEditingNotifier mockNewsEditingNotifier;

    setUp(() {
      mockNewsEditingNotifier = MockNewsEditingNotifier(const NewsEditingState());
    });

    Widget buildTestWidget({
      int? newsId,
      String initialContent = '',
      NewsEditingState state = const NewsEditingState(),
    }) {
      return ProviderScope(
        overrides: [
          newsEditingProvider
              .overrideWith((ref) => MockNewsEditingNotifier(state)),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: NewsInlineEditor(
              newsId: newsId,
              initialContent: initialContent,
            ),
          ),
        ),
      );
    }

    testWidgets('should display correctly for creating a new news item',
        (tester) async {
      await tester.pumpWidget(buildTestWidget());

      expect(find.text('Создание новости'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Опубликовать'), findsOneWidget);
      expect(find.text('Отмена'), findsOneWidget);
    });

    testWidgets('should display correctly for editing an existing news item',
        (tester) async {
      await tester.pumpWidget(buildTestWidget(
        newsId: 1,
        initialContent: 'Initial content',
      ));

      expect(find.text('Редактирование новости'), findsOneWidget);
      expect(find.text('Сохранить'), findsOneWidget);
      expect(find.text('Initial content'), findsOneWidget);
    });

    testWidgets('should call saveChanges when save button is tapped',
        (tester) async {
      when(() => mockNewsEditingNotifier.saveChanges())
          .thenAnswer((_) async => Future.value());

      await tester.pumpWidget(buildTestWidget(
        state: const NewsEditingState(editingContent: 'Some content'),
      ));

      await tester.tap(find.text('Опубликовать'));
      await tester.pump();

      verify(() => mockNewsEditingNotifier.saveChanges()).called(1);
    });

    testWidgets('should call cancelEditing when cancel button is tapped',
        (tester) async {
      await tester.pumpWidget(buildTestWidget());

      await tester.tap(find.text('Отмена'));
      await tester.pump();

      verify(() => mockNewsEditingNotifier.cancelEditing()).called(1);
    });

    testWidgets('save button should be disabled when content is empty',
        (tester) async {
      await tester.pumpWidget(buildTestWidget(
        state: const NewsEditingState(editingContent: ' '),
      ));

      final button = tester.widget<FilledButton>(find.byType(FilledButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should show loading indicator when saving', (tester) async {
      await tester.pumpWidget(buildTestWidget(
        state: const NewsEditingState(isSaving: true, editingContent: 'Content'),
      ));

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Опубликовать'), findsNothing);
    });
  });
}