import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/app_shell.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/core/models/data_state.dart';
import 'package:luxury_app/core/services/permission_service.dart';
import 'package:luxury_app/features/news/news_item.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:mocktail/mocktail.dart';

// Mocks
class MockPermissionService extends Mock implements PermissionService {}

class MockNewsNotifier extends StateNotifier<DataState<List<NewsItem>>>
    with Mo<PERSON>
    implements NewsNotifier {
  MockNewsNotifier(super.state);
}

void main() {
  group('AppShell AppBar Action Button', () {
    late MockPermissionService mockPermissionService;

    setUp(() {
      mockPermissionService = MockPermissionService();
    });

    Widget buildTestWidget({
      required DrawerMode activeScreen,
      required bool hasPermission,
      required DataState<List<NewsItem>> newsState,
    }) {
      return ProviderScope(
        overrides: [
          permissionServiceProvider.overrideWithValue(mockPermissionService),
          appProvider.overrideWith((ref) {
            final notifier = AppNotifier();
            notifier.state = AppState(activeScreen: activeScreen);
            return notifier;
          }),
          newsProvider.overrideWith((ref) => MockNewsNotifier(newsState)),
        ],
        child: const MaterialApp(
          home: AppShell(body: Center(child: Text('Test Body'))),
        ),
      );
    }

    testWidgets(
        'should show plus button on news screen when user has permission and news are loaded',
        (tester) async {
      when(() => mockPermissionService.hasPermission(Permissions.canCreateNews))
          .thenReturn(true);

      await tester.pumpWidget(buildTestWidget(
        activeScreen: DrawerMode.news,
        hasPermission: true,
        newsState: const DataState(data: []),
      ));

      await tester.pumpAndSettle();

      expect(find.byIcon(LucideIcons.plus), findsOneWidget);
    });

    testWidgets('should not show plus button when user has no permission',
        (tester) async {
      when(() => mockPermissionService.hasPermission(Permissions.canCreateNews))
          .thenReturn(false);

      await tester.pumpWidget(buildTestWidget(
        activeScreen: DrawerMode.news,
        hasPermission: false,
        newsState: const DataState(data: []),
      ));

      await tester.pumpAndSettle();

      expect(find.byIcon(LucideIcons.plus), findsNothing);
    });

    testWidgets('should not show plus button when news are loading',
        (tester) async {
      when(() => mockPermissionService.hasPermission(Permissions.canCreateNews))
          .thenReturn(true);

      await tester.pumpWidget(buildTestWidget(
        activeScreen: DrawerMode.news,
        hasPermission: true,
        newsState: const DataState(isLoading: true),
      ));

      await tester.pumpAndSettle();

      expect(find.byIcon(LucideIcons.plus), findsNothing);
    });

    testWidgets('should not show plus button on other screens',
        (tester) async {
      when(() => mockPermissionService.hasPermission(Permissions.canCreateNews))
          .thenReturn(true);

      await tester.pumpWidget(buildTestWidget(
        activeScreen: DrawerMode.chat,
        hasPermission: true,
        newsState: const DataState(data: []),
      ));

      await tester.pumpAndSettle();

      expect(find.byIcon(LucideIcons.plus), findsNothing);
    });
  });
}